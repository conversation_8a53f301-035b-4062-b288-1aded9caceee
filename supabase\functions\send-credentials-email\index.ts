import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.45.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Resend API configuration
const RESEND_API_KEY = Deno.env.get('RESEND_API_KEY')
const FROM_EMAIL = '<EMAIL>' // Verified email from Resend

interface SendEmailRequest {
  to: string;
  name: string;
  apartment: string;
  email: string;
  password: string;
}

Deno.serve(async (req) => {
  console.log('📧 [DEBUG] Send credentials email function started');

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const requestBody = await req.json();
    const { to, name, apartment, email, password }: SendEmailRequest = requestBody;

    console.log('📧 [DEBUG] Email request:', { to, name, apartment });

    // Validate required fields
    if (!to || !name || !apartment || !email || !password) {
      return new Response(
        JSON.stringify({ error: 'Campos obrigatórios em falta' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Get Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

    // Verify authentication
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Token de autorização necessário' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: userError } = await supabaseAdmin.auth.getUser(token);

    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Token inválido' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Check if user is admin
    const { data: profile } = await supabaseAdmin
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single();

    if (!profile || profile.role !== 'admin') {
      return new Response(
        JSON.stringify({ error: 'Permissões de administrador necessárias' }),
        { status: 403, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // Create email content
    const emailSubject = `Credenciais de Acesso - Condomínio Prédio Azul`;
    const emailHtml = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Credenciais de Acesso</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #0066B3; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .credentials { background: white; padding: 15px; border-left: 4px solid #0066B3; margin: 20px 0; }
        .footer { text-align: center; padding: 20px; font-size: 12px; color: #666; }
        .button { display: inline-block; padding: 12px 24px; background: #0066B3; color: white; text-decoration: none; border-radius: 4px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Condomínio Prédio Azul</h1>
            <p>Credenciais de Acesso</p>
        </div>
        <div class="content">
            <p>Olá <strong>${name}</strong>,</p>
            <p>Foram criadas as suas credenciais de acesso ao sistema do Condomínio Prédio Azul.</p>

            <div class="credentials">
                <h3>DADOS DE ACESSO:</h3>
                <p><strong>Nome:</strong> ${name}</p>
                <p><strong>Apartamento:</strong> ${apartment}</p>
                <p><strong>Email:</strong> ${email}</p>
                <p><strong>Senha:</strong> ${password}</p>
            </div>

            <h3>INSTRUÇÕES:</h3>
            <ol>
                <li>Acesse o sistema através do link abaixo</li>
                <li>Use o email e senha fornecidos para fazer login</li>
                <li>Será solicitado que altere a senha no primeiro acesso</li>
                <li>Mantenha suas credenciais em segurança</li>
            </ol>

            <div style="text-align: center;">
                <a href="https://predioazul.com" class="button">Acessar Sistema</a>
            </div>

            <p>Em caso de dúvidas, entre em contacto com a administração do condomínio.</p>
        </div>
        <div class="footer">
            <p>Atenciosamente,<br>Administração do Condomínio Prédio Azul</p>
            <hr>
            <p>Esta é uma mensagem automática. Não responda a este email.<br>
            Data: ${new Date().toLocaleString('pt-PT')}</p>
        </div>
    </div>
</body>
</html>
    `.trim();

    const emailText = `
Olá ${name},

Foram criadas as suas credenciais de acesso ao sistema do Condomínio Prédio Azul.

DADOS DE ACESSO:
• Nome: ${name}
• Apartamento: ${apartment}
• Email: ${email}
• Senha: ${password}

INSTRUÇÕES:
1. Acesse o sistema através do link: https://predioazul.com
2. Use o email e senha fornecidos para fazer login
3. Será solicitado que altere a senha no primeiro acesso
4. Mantenha suas credenciais em segurança

Em caso de dúvidas, entre em contacto com a administração do condomínio.

Atenciosamente,
Administração do Condomínio Prédio Azul

---
Esta é uma mensagem automática. Não responda a este email.
Data: ${new Date().toLocaleString('pt-PT')}
    `.trim();

    // Send email using Resend API
    console.log('📧 [DEBUG] Email content prepared:');
    console.log('To:', to);
    console.log('Subject:', emailSubject);
    console.log('Text preview:', emailText.substring(0, 100) + '...');

    let emailResult: any = null;

    try {
      // Check if Resend API key is configured
      if (!RESEND_API_KEY) {
        console.warn('⚠️ [DEBUG] RESEND_API_KEY not configured, simulating email send');

        // Simulate email sending delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        emailResult = {
          id: 'simulated-' + Date.now(),
          simulated: true
        };
      } else {
        // Send real email using Resend
        console.log('📧 [DEBUG] Sending real email via Resend...');

        const emailPayload = {
          from: FROM_EMAIL,
          to: [to],
          subject: emailSubject,
          html: emailHtml,
          text: emailText
        };

        const resendResponse = await fetch('https://api.resend.com/emails', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${RESEND_API_KEY}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(emailPayload),
        });

        if (!resendResponse.ok) {
          const errorData = await resendResponse.json();
          throw new Error(`Resend API error: ${errorData.message || 'Unknown error'}`);
        }

        emailResult = await resendResponse.json();
        console.log('✅ [DEBUG] Email sent successfully via Resend:', emailResult.id);
      }

      const result = {
        success: true,
        message: 'Email enviado com sucesso',
        details: {
          to,
          subject: emailSubject,
          timestamp: new Date().toISOString(),
          emailId: emailResult?.id,
          simulated: emailResult?.simulated || false
        }
      };

      return new Response(
        JSON.stringify(result),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );

    } catch (emailError) {
      console.error('❌ [DEBUG] Error sending email:', emailError);

      // Return error but don't fail completely
      const errorResult = {
        success: false,
        message: 'Erro ao enviar email',
        error: emailError instanceof Error ? emailError.message : 'Unknown email error',
        details: {
          to,
          subject: emailSubject,
          timestamp: new Date().toISOString(),
          simulated: false
        }
      };

      return new Response(
        JSON.stringify(errorResult),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

  } catch (error) {
    console.error('💥 [DEBUG] Error in send-credentials-email:', error);
    return new Response(
      JSON.stringify({
        error: 'Erro interno do servidor',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
  }
});
