-- Limpeza de funções RPC desnecessárias após Edge Function funcionar perfeitamente
-- Data: 17/06/2025, 19:15
-- Motivo: Trigger removido, Edge Function funciona, funções RPC não são mais necessárias

-- 1. Remover função create_resident_user (não é mais necessária)
DROP FUNCTION IF EXISTS public.create_resident_user(
  p_email TEXT,
  p_password TEXT,
  p_name TEXT,
  p_apartment_id TEXT,
  p_admin_id UUID
);

-- 2. Remover função prepare_resident_user se existir (era usada como fallback)
DROP FUNCTION IF EXISTS public.prepare_resident_user(
  p_email TEXT,
  p_name TEXT,
  p_apartment_id TEXT,
  p_admin_user_id UUID
);

-- Comentário: Mantemos as seguintes funções RPC que ainda são necessárias:
-- - get_residents_with_access_status() - Para listagem com JOINs complexos
-- - revoke_apartment_access() - Para revogação de acessos
-- - delete_user_completely() - Para deletar usuários (precisa service role)
-- - is_current_user_admin() - Para políticas RLS
