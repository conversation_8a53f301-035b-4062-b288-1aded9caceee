# Deploy da Edge Function send-credentials-email

## 📧 Como fazer o deploy da Edge Function

### 1. Via Supabase Dashboard

1. Acesse o **Supabase Dashboard** do seu projeto
2. Vá para **Edge Functions** no menu lateral
3. <PERSON><PERSON> em **"Deploy a new function"**
4. <PERSON><PERSON><PERSON><PERSON> **"Upload from local"**
5. Faça upload do arquivo `supabase/functions/send-credentials-email/index.ts`
6. Nome da função: `send-credentials-email`

### 2. Via CLI (se tiver instalado)

```bash
# Instalar Supabase CLI (se não tiver)
npm install -g supabase

# Login no Supabase
supabase login

# Deploy da função
supabase functions deploy send-credentials-email
```

### 3. Configurar Variáveis de Ambiente

No **Supabase Dashboard > Settings > Edge Functions**, adicione:

```
RESEND_API_KEY=re_xxxxxxxxxx
```

**Como obter a RESEND_API_KEY:**
1. <PERSON><PERSON> [resend.com](https://resend.com)
2. Crie uma conta ou faça login
3. Vá para **API Keys**
4. Crie uma nova API Key
5. Copie a chave que começa com `re_`

### 4. Verificar Domínio no Resend

1. No Resend Dashboard, vá para **Domains**
2. Adicione o domínio `carloscesarportfolio.com`
3. Configure os registros DNS conforme instruído
4. Aguarde verificação

### 5. Testar a Função

Após o deploy, a função estará disponível em:
```
https://qrcegsdhbwgjtkebqjok.supabase.co/functions/v1/send-credentials-email
```

## ✅ Alterações Implementadas

- ✅ **Email verificado atualizado**: `<EMAIL>`
- ✅ **Removida lógica de cópia para condomínio**
- ✅ **Interface simplificada** (apenas opção de envio para morador)
- ✅ **Edge Function otimizada** para envio direto

## 🎯 Resultado Final

Após o deploy:
1. **Credenciais aparecerão** imediatamente após criação
2. **Emails serão enviados** via Resend (ou simulados se API key não configurada)
3. **Interface limpa** sem opções desnecessárias
4. **Feedback adequado** para todas as operações
