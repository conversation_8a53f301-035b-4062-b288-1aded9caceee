/**
 * Dashboard Administrativo - Página Principal do Sistema
 *
 * Esta página exibe uma visão geral completa do condomínio, incluindo:
 * - Estatísticas de quotas e moradores
 * - Gráficos de desempenho financeiro
 * - Atividades recentes do sistema
 * - Resumos financeiros e de moradores
 *
 * <AUTHOR> Prédio Azul
 * @version 1.0
 */

import React from 'react';
import AdminLayout from '@/components/layout/admin/AdminLayout';
import DashboardCard from '@/components/dashboard/DashboardCard';
import Chart from '@/components/dashboard/Chart';
import { DashboardErrorSkeleton } from '@/components/dashboard/DashboardSkeleton';
import { useDashboardData } from '@/hooks/useDashboardData';
import { toast } from 'sonner';
import {
  AlertTriangle,
  CreditCard,
  TrendingUp,
  Users,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  RefreshCw
} from 'lucide-react';

/**
 * Componente Principal do Dashboard Administrativo
 *
 * Gerencia o estado e exibição de todos os dados do dashboard,
 * incluindo estatísticas, gráficos e atividades recentes.
 *
 * @returns {JSX.Element} Página completa do dashboard
 */
const AdminDashboard = () => {
  // Hook personalizado para buscar todos os dados do dashboard
  const {
    stats,                // Estatísticas gerais (quotas, moradores, etc.)
    quotasSummary,        // Resumo das quotas do mês atual
    financialSummary,     // Resumo financeiro (entradas, saídas, saldo)
    chartData,            // Dados para os gráficos
    recentActivities,     // Atividades recentes do sistema
    activeMoradores,      // Número de moradores ativos
    isLoading,            // Estado de carregamento
    isError,              // Estado de erro
    error,                // Objeto de erro
    refetch               // Função para recarregar dados
  } = useDashboardData();

  /**
   * Formata valores monetários para o padrão angolano
   * @param {number} value - Valor a ser formatado
   * @returns {string} Valor formatado em Kwanzas
   */
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-AO', {
      style: 'currency',
      currency: 'AOA',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(value).replace('AOA', 'Kz');
  };

  /**
   * Formata valores percentuais
   * @param {number} value - Valor percentual a ser formatado
   * @returns {string} Valor formatado como percentual
   */
  const formatPercentage = (value: number) => {
    return `${Math.round(value)}%`;
  };

  /**
   * Recarrega todos os dados do dashboard
   * Exibe notificação de carregamento e sucesso
   */
  const handleRetry = () => {
    toast.loading('Recarregando dados...', { id: 'dashboard-retry' });
    refetch();
    setTimeout(() => {
      toast.dismiss('dashboard-retry');
      toast.success('Dados atualizados!');
    }, 1000);
  };

  // Renderização condicional: Se houver erro, mostrar tela de erro
  if (isError && !isLoading) {
    return (
      <AdminLayout
        title="Painel de Controle"
        subtitle="Bem-vindo ao sistema de gestão Prédio Azul"
      >
        <DashboardErrorSkeleton
          onRetry={handleRetry}
          message={error?.message || "Erro ao carregar dados do dashboard"}
        />
      </AdminLayout>
    );
  }

  return (
    <AdminLayout
      title="Painel de Controle"
      subtitle="Bem-vindo ao sistema de gestão Prédio Azul"
    >
      {/* Botão de atualização manual dos dados */}
      <div className="flex justify-end mb-4">
        <button
          onClick={handleRetry}
          disabled={isLoading}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
          Atualizar
        </button>
      </div>

      {/* Seção de Cards Estatísticos - Grid responsivo com 4 métricas principais */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mt-6 animate-enter">
        {/* Card 1: Quotas em Atraso - Indicador crítico */}
        <DashboardCard
          title="Quotas em Atraso"
          value={stats?.quotasEmAtraso || 0}
          description={`${formatPercentage(stats?.percentualAtraso || 0)} do total de moradores`}
          icon={AlertTriangle}
          iconBgColor="bg-red-100"
          iconColor="text-red-500"
          isLoading={isLoading}
        />

        {/* Card 2: Arrecadação Mensal - Receita do mês atual */}
        <DashboardCard
          title="Arrecadação do Mês"
          value={stats ? formatCurrency(stats.arrecadacaoMes) : '0 Kz'}
          trend={stats ? {
            value: Math.abs(stats.tendenciaArrecadacao),
            isPositive: stats.tendenciaArrecadacao >= 0
          } : undefined}
          description="vs. mês anterior"
          icon={CreditCard}
          iconBgColor="bg-primary-100"
          iconColor="text-primary-500"
          isLoading={isLoading}
        />

        {/* Card 3: Moradores com Multas - Indicador de inadimplência */}
        <DashboardCard
          title="Moradores com Multas"
          value={stats?.moradoresComMultas || 0}
          trend={stats ? {
            value: Math.abs(stats.tendenciaMultas),
            isPositive: stats.tendenciaMultas <= 0
          } : undefined}
          description="vs. mês anterior"
          icon={Users}
          iconBgColor="bg-orange-100"
          iconColor="text-orange-500"
          isLoading={isLoading}
        />

        {/* Card 4: Taxa de Pagamento - Indicador de performance */}
        <DashboardCard
          title="Taxa de Pagamento"
          value={stats ? formatPercentage(stats.taxaPagamento) : '0%'}
          trend={stats ? {
            value: Math.abs(stats.tendenciaPagamento),
            isPositive: stats.tendenciaPagamento >= 0
          } : undefined}
          description="vs. mês anterior"
          icon={TrendingUp}
          iconBgColor="bg-green-100"
          iconColor="text-green-500"
          isLoading={isLoading}
        />
      </div>

      {/* Seção de Gráficos - Análise visual dos dados financeiros */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
        {/* Gráfico 1: Quotas Mensais - Barras comparativas */}
        <Chart
          type="bar"
          data={chartData?.monthlyQuotas || []}
          title="Quotas Mensais"
          subtitle="Comparativo de quotas pagas e pendentes"
          dataKeys={['pagas', 'pendentes']}
          height={300}
          colors={['#019cdf', '#FDA349']}
          isLoading={isLoading}
        />

        {/* Gráfico 2: Fluxo de Caixa - Área temporal */}
        <Chart
          type="area"
          data={chartData?.cashFlow || []}
          title="Fluxo de Caixa"
          subtitle="Entradas e saídas mensais"
          dataKeys={['entrada', 'saida']}
          height={300}
          colors={['#45B69C', '#F87171']}
          isLoading={isLoading}
        />
      </div>

      {/* Seção Inferior - Atividades Recentes e Resumos Laterais */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-8">
        {/* Coluna Principal: Atividades Recentes (2/3 da largura) */}
        <div className="lg:col-span-2">
          <div className="dashboard-card flex flex-col" style={{ height: '650px' }}>
            <h3 className="text-base font-medium text-gray-700 mb-4 flex-shrink-0">Atividades Recentes</h3>
            {/* Container com scroll para lista de atividades */}
            <div className="flex-1 overflow-y-auto pr-2 min-h-0">
              {isLoading ? (
                <div className="space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <div className="animate-pulse bg-gray-200 rounded-full h-10 w-10" />
                        <div className="ml-3">
                          <div className="animate-pulse bg-gray-200 rounded h-4 w-32 mb-1" />
                          <div className="animate-pulse bg-gray-200 rounded h-3 w-24" />
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="animate-pulse bg-gray-200 rounded h-4 w-20 mb-1" />
                        <div className="animate-pulse bg-gray-200 rounded h-3 w-16" />
                      </div>
                    </div>
                  ))}
                </div>
              ) : recentActivities && recentActivities.length > 0 ? (
                <div className="space-y-3">
                  {recentActivities.map((activity) => (
                    <div key={activity.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <div className={`h-10 w-10 rounded-full flex items-center justify-center ${
                          activity.type === 'quota_paid' ? 'bg-green-100' :
                          activity.type === 'fine_applied' ? 'bg-red-100' :
                          activity.type === 'new_resident' ? 'bg-blue-100' :
                          activity.type === 'expense_added' ? 'bg-gray-100' :
                          activity.type === 'income_added' ? 'bg-green-100' :
                          'bg-gray-100'
                        }`}>
                          {activity.type === 'quota_paid' && <CreditCard size={18} className="text-green-500" />}
                          {activity.type === 'fine_applied' && <AlertTriangle size={18} className="text-red-500" />}
                          {activity.type === 'new_resident' && <Users size={18} className="text-blue-500" />}
                          {activity.type === 'expense_added' && <ArrowDownRight size={18} className="text-red-500" />}
                          {activity.type === 'income_added' && <ArrowUpRight size={18} className="text-green-500" />}
                        </div>
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-800">{activity.title}</p>
                          <p className="text-xs text-gray-500">{activity.description}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        {activity.amount && (
                          <p className={`text-sm font-semibold ${
                            activity.type === 'expense_added' ? 'text-red-600' : 'text-green-600'
                          }`}>
                            {activity.type === 'expense_added' ? '-' : '+'}{formatCurrency(activity.amount)}
                          </p>
                        )}
                        <p className="text-xs text-gray-500">
                          {new Date(activity.date).toLocaleDateString('pt-BR')}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Nenhuma atividade recente</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Coluna Lateral: Resumos Financeiro e de Moradores (1/3 da largura) */}
        <div className="space-y-6">
          {/* Card 1: Resumo Financeiro - Entradas, saídas e saldos */}
          <div className="dashboard-card">
            <h3 className="text-base font-medium text-gray-700 mb-4">Resumo Financeiro</h3>
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div className="animate-pulse bg-gray-200 rounded h-4 w-40" />
                    <div className="animate-pulse bg-gray-200 rounded h-4 w-24" />
                  </div>
                ))}
              </div>
            ) : financialSummary ? (
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-600">Total Entradas</span>
                  <span className="text-sm font-semibold text-green-600">
                    {formatCurrency(financialSummary.totalEntradas)}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-600">Total Saídas</span>
                  <span className="text-sm font-semibold text-red-600">
                    {formatCurrency(financialSummary.totalSaidas)}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-600">Saldo Atual</span>
                  <span className={`text-sm font-semibold ${
                    financialSummary.saldoAtual >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {formatCurrency(financialSummary.saldoAtual)}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-600">Resultado do Mês</span>
                  <span className={`text-sm font-semibold ${
                    financialSummary.saldoMes >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {financialSummary.saldoMes >= 0 ? '+' : ''}{formatCurrency(financialSummary.saldoMes)}
                  </span>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-500">Dados não disponíveis</p>
              </div>
            )}
          </div>

          {/* Card 2: Resumo de Moradores - Estatísticas de quotas e pagamentos */}
          <div className="dashboard-card">
            <h3 className="text-base font-medium text-gray-700 mb-4">Resumo de Moradores</h3>
            {isLoading ? (
              <div className="space-y-4">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div className="animate-pulse bg-gray-200 rounded h-4 w-32" />
                    <div className="animate-pulse bg-gray-200 rounded h-4 w-20" />
                  </div>
                ))}
              </div>
            ) : (stats && quotasSummary) ? (
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-600">Moradores Ativos</span>
                  <span className="text-sm font-semibold">{activeMoradores || stats.moradoresAtivos}</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-600">Com Quotas em Dia</span>
                  <span className="text-sm font-semibold text-green-600">
                    {quotasSummary.quotasPagas} ({formatPercentage(quotasSummary.percentualPago)})
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-600">Com Quotas em Atraso</span>
                  <span className="text-sm font-semibold text-red-600">
                    {stats.quotasEmAtraso} ({formatPercentage(stats.percentualAtraso)})
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-600">Taxa de Pagamento</span>
                  <span className={`text-sm font-semibold ${
                    stats.taxaPagamento >= 80 ? 'text-green-600' :
                    stats.taxaPagamento >= 60 ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {formatPercentage(stats.taxaPagamento)}
                  </span>
                </div>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-gray-500">Dados não disponíveis</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminDashboard;
