import { useQuery } from '@tanstack/react-query';
import { getConfiguracaoByNome } from '@/utils/supabase-helpers';

export interface CommitteeRole {
  id: string;
  name: string;
  description?: string;
  active: boolean;
}

export const useCommitteeRoles = () => {
  // Fetch committee roles from configuration
  const { data: rolesConfig, isLoading, error } = useQuery({
    queryKey: ['committee-roles'],
    queryFn: () => getConfiguracaoByNome('funcoes_comissao'),
  });

  // Parse and return roles
  const roles: CommitteeRole[] = (() => {
    if (rolesConfig) {
      try {
        const parsedRoles = JSON.parse(rolesConfig);
        return Array.isArray(parsedRoles) ? parsedRoles : [];
      } catch (error) {
        console.error('Error parsing committee roles:', error);
        return [];
      }
    } else {
      // Return default roles if none exist
      return [
        { id: '1', name: 'President<PERSON>', description: 'Responsável pela liderança geral da comissão', active: true },
        { id: '2', name: 'Vice-President<PERSON>', description: 'Substituto do presidente quando necessário', active: true },
        { id: '3', name: 'Tesoureiro', description: 'Responsável pela gestão financeira', active: true },
        { id: '4', name: 'Secretário', description: 'Responsável pela documentação e comunicação', active: true },
      ];
    }
  })();

  // Filter only active roles
  const activeRoles = roles.filter(role => role.active);

  return {
    roles,
    activeRoles,
    isLoading,
    error
  };
};
