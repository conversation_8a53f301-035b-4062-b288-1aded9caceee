import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { AlertTriangle, ChevronDown } from 'lucide-react';

interface IconFineStatusFilterProps {
  selectedFineStatus: string | null;
  onFineStatusChange: (status: string | null) => void;
  className?: string;
}

const fineStatusOptions = [
  { value: null, label: 'Todas as situações', color: 'text-gray-600' },
  { value: 'Regularizada', label: 'Regularizada', color: 'text-green-600' },
  { value: 'Não Regularizada', label: 'Não Regularizada', color: 'text-orange-600' }
];

const IconFineStatusFilter: React.FC<IconFineStatusFilterProps> = ({
  selectedFineStatus,
  onFineStatusChange,
  className
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleFineStatusSelect = (status: string | null) => {
    onFineStatusChange(status);
    setIsOpen(false);
  };

  const selectedOption = fineStatusOptions.find(option => option.value === selectedFineStatus);
  const isActive = selectedFineStatus !== null;

  return (
    <div className={cn("relative", className)} ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          "flex items-center gap-2 px-3 py-2 rounded-lg border transition-all duration-200",
          "hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-1",
          isActive 
            ? "bg-orange-50 border-orange-200 text-orange-700 shadow-sm" 
            : "bg-white border-gray-300 text-gray-600 hover:border-gray-400"
        )}
        title={`Filtrar por situação da multa: ${selectedOption?.label || 'Todas'}`}
      >
        <AlertTriangle 
          size={18} 
          className={cn(
            "transition-colors duration-200",
            isActive ? "text-orange-600" : "text-gray-500"
          )} 
        />
        <span className="text-sm font-medium">Multa</span>
        {isActive && (
          <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
        )}
        <ChevronDown 
          size={16} 
          className={cn(
            "transition-transform duration-200",
            isOpen && "transform rotate-180"
          )} 
        />
      </button>

      {/* Dropdown */}
      {isOpen && (
        <div className="absolute z-50 mt-2 w-52 bg-white border border-gray-200 rounded-lg shadow-lg animate-in slide-in-from-top-2 duration-200">
          {fineStatusOptions.map((option) => (
            <button
              key={option.value || 'all'}
              className={cn(
                "w-full px-3 py-2 text-left hover:bg-gray-50 transition-colors duration-150",
                "first:rounded-t-lg last:rounded-b-lg",
                selectedFineStatus === option.value && "bg-primary-50 text-primary-700"
              )}
              onClick={() => handleFineStatusSelect(option.value)}
            >
              <div className="flex items-center gap-2">
                <AlertTriangle size={16} className={option.color} />
                <span className="text-sm">{option.label}</span>
                {selectedFineStatus === option.value && (
                  <div className="ml-auto w-2 h-2 bg-primary-500 rounded-full"></div>
                )}
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default IconFineStatusFilter;
