-- =====================================================
-- BACKUP DO TRIGGER E FUNÇÃO handle_new_user
-- Data: 17/06/2025
-- Motivo: Conflito com criação manual de perfis
-- =====================================================

-- FUNÇÃO ORIGINAL
CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  INSERT INTO public.profiles (id, name, role, email)
  VALUES (
    new.id, 
    new.raw_user_meta_data->>'name', 
    COALESCE(new.raw_user_meta_data->>'role', 'convidado'),
    new.email
  );
  RETURN new;
END;
$function$;

-- TRIGGER ORIGINAL
CREATE TRIGGER on_auth_user_created 
  AFTER INSERT ON auth.users 
  FOR EACH ROW 
  EXECUTE FUNCTION handle_new_user();

-- =====================================================
-- COMANDOS PARA RESTAURAR (se necessário):
-- =====================================================
-- 1. Executar a função acima
-- 2. Executar o trigger acima
-- 3. Testar se funciona corretamente

-- =====================================================
-- COMANDOS PARA REMOVER (atual):
-- =====================================================
-- DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
-- DROP FUNCTION IF EXISTS public.handle_new_user();
