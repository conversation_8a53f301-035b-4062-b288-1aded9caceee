import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Plus, Edit, Trash2, Users, LoaderCircle } from 'lucide-react';
import { toast } from 'sonner';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getConfiguracaoByNome, updateConfiguracao } from '@/utils/supabase-helpers';

export interface CommitteeRole {
  id: string;
  name: string;
  description?: string;
  active: boolean;
}

const CommitteeRolesManager = () => {
  const [roles, setRoles] = useState<CommitteeRole[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<CommitteeRole | null>(null);
  const [roleToDelete, setRoleToDelete] = useState<CommitteeRole | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    active: true
  });

  const queryClient = useQueryClient();

  // Fetch committee roles from configuration
  const { data: rolesConfig, isLoading } = useQuery({
    queryKey: ['committee-roles'],
    queryFn: () => getConfiguracaoByNome('funcoes_comissao'),
  });

  // Parse roles from configuration
  useEffect(() => {
    if (rolesConfig) {
      try {
        const parsedRoles = JSON.parse(rolesConfig);
        setRoles(Array.isArray(parsedRoles) ? parsedRoles : []);
      } catch (error) {
        console.error('Error parsing committee roles:', error);
        setRoles([]);
      }
    } else {
      // Set default roles if none exist
      setRoles([
        { id: '1', name: 'Presidente', description: 'Responsável pela liderança geral da comissão', active: true },
        { id: '2', name: 'Vice-Presidente', description: 'Substituto do presidente quando necessário', active: true },
        { id: '3', name: 'Tesoureiro', description: 'Responsável pela gestão financeira', active: true },
        { id: '4', name: 'Secretário', description: 'Responsável pela documentação e comunicação', active: true },
      ]);
    }
  }, [rolesConfig]);

  // Save roles mutation
  const saveRolesMutation = useMutation({
    mutationFn: (rolesToSave: CommitteeRole[]) => {
      return updateConfiguracao('funcoes_comissao', JSON.stringify(rolesToSave));
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['committee-roles'] });
      toast.success('Funções da comissão salvas com sucesso!');
    },
    onError: (error) => {
      console.error('Error saving committee roles:', error);
      toast.error('Erro ao salvar funções da comissão');
    }
  });

  const handleOpenModal = (role?: CommitteeRole) => {
    if (role) {
      setEditingRole(role);
      setFormData({
        name: role.name,
        description: role.description || '',
        active: role.active
      });
    } else {
      setEditingRole(null);
      setFormData({
        name: '',
        description: '',
        active: true
      });
    }
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingRole(null);
    setFormData({
      name: '',
      description: '',
      active: true
    });
  };

  const handleSaveRole = () => {
    if (!formData.name.trim()) {
      toast.error('Nome da função é obrigatório');
      return;
    }

    const newRoles = [...roles];
    
    if (editingRole) {
      // Update existing role
      const index = newRoles.findIndex(r => r.id === editingRole.id);
      if (index !== -1) {
        newRoles[index] = {
          ...editingRole,
          name: formData.name.trim(),
          description: formData.description.trim(),
          active: formData.active
        };
      }
    } else {
      // Add new role
      const newRole: CommitteeRole = {
        id: Date.now().toString(),
        name: formData.name.trim(),
        description: formData.description.trim(),
        active: formData.active
      };
      newRoles.push(newRole);
    }

    setRoles(newRoles);
    saveRolesMutation.mutate(newRoles);
    handleCloseModal();
  };

  const handleDeleteRole = (role: CommitteeRole) => {
    setRoleToDelete(role);
  };

  const confirmDelete = () => {
    if (roleToDelete) {
      const newRoles = roles.filter(r => r.id !== roleToDelete.id);
      setRoles(newRoles);
      saveRolesMutation.mutate(newRoles);
      setRoleToDelete(null);
    }
  };

  const toggleRoleStatus = (roleId: string) => {
    const newRoles = roles.map(role => 
      role.id === roleId ? { ...role, active: !role.active } : role
    );
    setRoles(newRoles);
    saveRolesMutation.mutate(newRoles);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoaderCircle className="animate-spin mr-2" size={20} />
        <span>Carregando funções...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-semibold text-gray-800">Funções da Comissão</h3>
          <p className="text-sm text-gray-600">Gerencie as funções disponíveis para os membros da comissão</p>
        </div>
        <Button onClick={() => handleOpenModal()} className="flex items-center gap-2">
          <Plus size={16} />
          Nova Função
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {roles.map((role) => (
          <Card key={role.id} className={`${!role.active ? 'opacity-60' : ''}`}>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-base">{role.name}</CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant={role.active ? "default" : "secondary"}>
                    {role.active ? 'Ativo' : 'Inativo'}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              {role.description && (
                <p className="text-sm text-gray-600 mb-4">{role.description}</p>
              )}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Switch
                    checked={role.active}
                    onCheckedChange={() => toggleRoleStatus(role.id)}
                  />
                  <span className="text-sm text-gray-600">
                    {role.active ? 'Ativo' : 'Inativo'}
                  </span>
                </div>
                <div className="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleOpenModal(role)}
                  >
                    <Edit size={14} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDeleteRole(role)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 size={14} />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Add/Edit Role Modal */}
      <Dialog open={isModalOpen} onOpenChange={handleCloseModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingRole ? 'Editar Função' : 'Nova Função'}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="name">Nome da Função *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Ex: Presidente, Tesoureiro..."
              />
            </div>
            <div>
              <Label htmlFor="description">Descrição</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Descreva as responsabilidades desta função..."
                rows={3}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="active"
                checked={formData.active}
                onCheckedChange={(checked) => setFormData({ ...formData, active: checked })}
              />
              <Label htmlFor="active">Função ativa</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={handleCloseModal}>
              Cancelar
            </Button>
            <Button 
              onClick={handleSaveRole}
              disabled={saveRolesMutation.isPending}
            >
              {saveRolesMutation.isPending ? (
                <>
                  <LoaderCircle className="animate-spin mr-2" size={16} />
                  Salvando...
                </>
              ) : (
                editingRole ? 'Atualizar' : 'Criar'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={roleToDelete !== null} onOpenChange={(open) => !open && setRoleToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Excluir Função</AlertDialogTitle>
            <AlertDialogDescription>
              Tem certeza que deseja excluir a função "{roleToDelete?.name}"?
              Esta ação não pode ser desfeita.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              className="bg-red-600 hover:bg-red-700 focus:ring-red-600"
            >
              Excluir
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default CommitteeRolesManager;
