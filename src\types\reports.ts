/**
 * Tipos e interfaces para o sistema de relatórios
 * Sistema Prédio Azul - Módulo de Relatórios
 */

// Tipos de relatórios disponíveis
export type ReportType = 'quotas' | 'residents' | 'financial' | 'committee' | 'fines';

// Status dos relatórios
export type ReportStatus = 'generating' | 'completed' | 'error';

// Filtros base para todos os relatórios
export interface BaseReportFilters {
  startDate?: string;
  endDate?: string;
  includeDetails?: boolean;
}

// Filtros específicos para relatório de quotas
export interface QuotasReportFilters extends BaseReportFilters {
  status?: 'all' | 'paid' | 'pending' | 'overdue';
  apartmentNumber?: string;
  includeExempt?: boolean;
  includeFines?: boolean;
}

// Filtros específicos para relatório de moradores
export interface ResidentsReportFilters extends BaseReportFilters {
  status?: 'all' | 'active' | 'inactive';
  apartmentNumber?: string;
  includeContact?: boolean;
  includeExempt?: boolean;
}

// Filtros específicos para relatório financeiro
export interface FinancialReportFilters extends BaseReportFilters {
  type?: 'all' | 'income' | 'expense';
  category?: string;
  includeBalance?: boolean;
  groupByMonth?: boolean;
}

// Filtros específicos para relatório da comissão
export interface CommitteeReportFilters extends BaseReportFilters {
  includeContact?: boolean;
}

// Filtros específicos para relatório de multas
export interface FinesReportFilters extends BaseReportFilters {
  status?: 'all' | 'paid' | 'pending';
  apartmentNumber?: string;
  minAmount?: number;
}

// União de todos os tipos de filtros
export type ReportFilters =
  | QuotasReportFilters
  | ResidentsReportFilters
  | FinancialReportFilters
  | CommitteeReportFilters
  | FinesReportFilters;

// Dados base para relatórios
export interface BaseReportData {
  title: string;
  subtitle: string;
  generatedAt: string;
  generatedBy: string;
  filters: ReportFilters;
  totalRecords: number;
}

// Dados específicos para relatório de quotas
export interface QuotasReportData extends BaseReportData {
  type: 'quotas';
  summary: {
    totalQuotas: number;
    paidQuotas: number;
    pendingQuotas: number;
    overdueQuotas: number;
    totalAmount: number;
    paidAmount: number;
    pendingAmount: number;
    finesAmount: number;
  };
  quotas: Array<{
    id: string;
    residentName: string;
    apartmentNumber: string;
    month: number;
    year: number;
    amount: number;
    status: string;
    dueDate: string;
    paymentDate?: string;
    fine: number;
    situation: string;
  }>;
}

// Dados específicos para relatório de moradores
export interface ResidentsReportData extends BaseReportData {
  type: 'residents';
  summary: {
    totalResidents: number;
    activeResidents: number;
    inactiveResidents: number;
    exemptResidents: number;
  };
  residents: Array<{
    id: string;
    name: string;
    apartmentNumber: string;
    status: string;
    phone?: string;
    email?: string;
    isExempt: boolean;
    registrationDate: string;
  }>;
}

// Dados específicos para relatório financeiro
export interface FinancialReportData extends BaseReportData {
  type: 'financial';
  summary: {
    totalIncome: number;
    totalExpense: number;
    balance: number;
    incomeCount: number;
    expenseCount: number;
  };
  transactions: Array<{
    id: string;
    type: 'entrada' | 'saida';
    category: string;
    description: string;
    amount: number;
    date: string;
    responsible?: string;
  }>;
  monthlyBreakdown?: Array<{
    month: string;
    income: number;
    expense: number;
    balance: number;
  }>;
}

// Dados específicos para relatório da comissão
export interface CommitteeReportData extends BaseReportData {
  type: 'committee';
  summary: {
    totalMembers: number;
    activeMembers: number;
  };
  members: Array<{
    id: string;
    name: string;
    position: string;
    phone?: string;
    email?: string;
    startDate: string;
    endDate?: string;
    status: string;
  }>;
}

// Dados específicos para relatório de multas
export interface FinesReportData extends BaseReportData {
  type: 'fines';
  summary: {
    totalFines: number;
    paidFines: number;
    pendingFines: number;
    totalAmount: number;
    paidAmount: number;
    pendingAmount: number;
  };
  fines: Array<{
    id: string;
    residentName: string;
    apartmentNumber: string;
    month: number;
    year: number;
    fineAmount: number;
    status: string;
    appliedDate: string;
    paidDate?: string;
    reason: string;
  }>;
}

// União de todos os tipos de dados de relatórios
export type ReportData =
  | QuotasReportData
  | ResidentsReportData
  | FinancialReportData
  | CommitteeReportData
  | FinesReportData;

// Interface para histórico de relatórios
export interface ReportHistory {
  id: string;
  type: ReportType;
  title: string;
  generatedAt: string;
  generatedBy: string;
  status: ReportStatus;
  downloadUrl?: string;
  filters: ReportFilters;
}

// Interface para configurações de PDF
export interface PDFConfig {
  includeWatermark: boolean;
  includeLogo: boolean;
  pageNumbers: boolean;
  headerText?: string;
  footerText?: string;
  orientation: 'portrait' | 'landscape';
  fontSize: number;
}

// Interface para resposta da API de relatórios
export interface ReportResponse {
  success: boolean;
  data?: ReportData;
  error?: string;
  downloadUrl?: string;
}
