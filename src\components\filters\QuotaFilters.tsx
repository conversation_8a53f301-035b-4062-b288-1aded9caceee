import React from 'react';
import { cn } from '@/lib/utils';
import { RotateCcw } from 'lucide-react';
import ResidentFilter from './ResidentFilter';
import IconStatusFilter from './IconStatusFilter';
import IconFineStatusFilter from './IconFineStatusFilter';
import { Morador } from '@/types';

interface QuotaFiltersProps {
  moradores: Morador[];
  selectedMorador: Morador | null;
  onMoradorChange: (morador: Morador | null) => void;
  selectedStatus: string | null;
  onStatusChange: (status: string | null) => void;
  selectedFineStatus: string | null;
  onFineStatusChange: (status: string | null) => void;
  className?: string;
}

const QuotaFilters: React.FC<QuotaFiltersProps> = ({
  moradores,
  selectedMorador,
  onMoradorChange,
  selectedStatus,
  onStatusChange,
  selectedFineStatus,
  onFineStatusChange,
  className
}) => {
  const hasActiveFilters = selectedMorador || selectedStatus || selectedFineStatus;

  const handleClearAllFilters = () => {
    onMoradorChange(null);
    onStatusChange(null);
    onFineStatusChange(null);
  };

  return (
    <div className={cn("flex flex-col gap-3", className)}>
      {/* Filters Row - Optimized Layout */}
      <div className="flex flex-col sm:flex-row gap-3 sm:items-center">
        {/* Resident Filter */}
        <ResidentFilter
          moradores={moradores}
          selectedMorador={selectedMorador}
          onMoradorChange={onMoradorChange}
          className="w-full sm:w-64 flex-shrink-0"
        />

        {/* Icon Filters - Aligned on same baseline */}
        <div className="flex items-center gap-2">
          <IconStatusFilter
            selectedStatus={selectedStatus}
            onStatusChange={onStatusChange}
          />

          <IconFineStatusFilter
            selectedFineStatus={selectedFineStatus}
            onFineStatusChange={onFineStatusChange}
          />

          {/* Clear All Filters Button - Aligned with filters */}
          {hasActiveFilters && (
            <button
              onClick={handleClearAllFilters}
              className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg border border-gray-300 transition-all duration-200 hover:shadow-sm"
              title="Limpar todos os filtros"
            >
              <RotateCcw size={16} />
              <span className="hidden sm:inline">Limpar</span>
            </button>
          )}
        </div>
      </div>

      {/* Active Filters Summary - Compact Design */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 text-sm">
          {selectedMorador && (
            <span className="inline-flex items-center px-3 py-1 bg-primary-100 text-primary-800 rounded-full text-xs font-medium">
              👤 {selectedMorador.nome} ({selectedMorador.apartamento})
              <button
                onClick={() => onMoradorChange(null)}
                className="ml-2 hover:bg-primary-200 rounded-full p-0.5 transition-colors"
                title="Remover filtro de morador"
              >
                <RotateCcw size={10} />
              </button>
            </span>
          )}
          {selectedStatus && (
            <span className="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 rounded-full text-xs font-medium">
              ✅ {selectedStatus}
              <button
                onClick={() => onStatusChange(null)}
                className="ml-2 hover:bg-green-200 rounded-full p-0.5 transition-colors"
                title="Remover filtro de status"
              >
                <RotateCcw size={10} />
              </button>
            </span>
          )}
          {selectedFineStatus && (
            <span className="inline-flex items-center px-3 py-1 bg-orange-100 text-orange-800 rounded-full text-xs font-medium">
              ⚠️ {selectedFineStatus}
              <button
                onClick={() => onFineStatusChange(null)}
                className="ml-2 hover:bg-orange-200 rounded-full p-0.5 transition-colors"
                title="Remover filtro de multa"
              >
                <RotateCcw size={10} />
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default QuotaFilters;
